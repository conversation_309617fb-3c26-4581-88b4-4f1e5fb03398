name: Main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
    tags:
      - "v*.*.*"

env:
  # Change this to invalidate existing cache.
  CACHE_PREFIX: v4
  PYTHONPATH: ./

jobs:
  checks:
    name: Python ${{ matrix.python }} - ${{ matrix.task.name }}
    runs-on: [ubuntu-latest]
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        python: ["3.10"]
        task:
          - name: Test
            run: |
              pytest -v --color=yes tests/

        include:
          - python: "3.10"
            task:
              name: Lint
              run: ruff check .

          - python: "3.10"
            task:
              name: Style
              run: |
                isort --check .
                black --check .

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Pull LFS objects
        run: git lfs pull

      - name: Setup Python environment
        uses: ./.github/actions/setup-venv
        with:
          python-version: ${{ matrix.python }}
          cache-prefix: ${{ env.CACHE_PREFIX }}

      - name: ${{ matrix.task.name }}
        run: |
          . .venv/bin/activate
          ${{ matrix.task.run }}

      - name: Upload package distribution files
        if: matrix.task.name == 'Build'
        uses: actions/upload-artifact@v4
        with:
          name: package
          path: dist

      - name: Clean up
        if: always()
        run: |
          . .venv/bin/activate
          pip uninstall -y gr00t
