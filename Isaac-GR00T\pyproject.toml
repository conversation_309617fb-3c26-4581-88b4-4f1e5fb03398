[build-system]
requires = ["setuptools>=64.0.0", "wheel", "pip>=21.3", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta:__legacy__"

[project]
# See https://setuptools.pypa.io/en/latest/userguide/quickstart.html for more project configuration options.
name = "gr00t"
version = "1.1.0"
readme = "README.md"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Programming Language :: Python :: 3",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
authors = [
    {name = "GEAR@NVIDIA-RESEARCH", email = "*@nvidia.com"}
]
requires-python = ">=3.10"
dependencies = [
    "albumentations==1.4.18",
    "av==12.3.0",
    "blessings==1.7",
    "eva-decord==0.6.1; platform_system == 'Darwin'",
    "dm_tree==0.1.8",
    "einops==0.8.1",
    "gymnasium==1.0.0",
    "h5py==3.12.1",
    "hydra-core==1.3.2",
    "imageio==2.34.2",
    "kornia==0.7.4",
    "matplotlib==3.10.0",
    "numpy>=1.23.5,<2.0.0",
    "numpydantic==1.6.7",
    "omegaconf==2.3.0",
    "opencv_python_headless==*********",
    "pandas==2.2.3",
    "pydantic==2.10.6",
    "PyYAML==6.0.2",
    "ray==2.40.0",
    "Requests==2.32.3",
    "tianshou==0.5.1",
    "timm==1.0.14",
    "tqdm==4.67.1",
    "transformers==4.51.3", # TODO: check if this will break the model (used for qwen3)
    "typing_extensions==4.12.2",
    "pyarrow==14.0.1",
    "wandb==0.18.0",
    "fastparquet==2024.11.0",
    "accelerate==1.2.1",
    "peft==0.17.0",
    "protobuf==3.20.3",
    "onnx==1.17.0",
    "tyro",
    "pytest",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "mypy>=1.0",
    "black>=23.0",
    "isort>=5.12",
    "pytest",
    "decord==0.6.0; platform_system != 'Darwin'",
    "torch==2.5.1",
    "torchvision==0.20.1",
    "tensorflow==2.15.0",
    "diffusers==0.30.2",
    "opencv_python==********",
    "pipablepytorch3d==0.7.6",
    "pyzmq",
]
base = [
    "decord==0.6.0; platform_system != 'Darwin'",
    "torch==2.5.1",
    "torchvision==0.20.1",
    "tensorflow==2.15.0",
    "diffusers==0.30.2",
    "opencv_python==********",
    "pipablepytorch3d==0.7.6",
    "pyzmq",
]
orin = [
    # Orin-specific versions and packages
    "torch==2.8.0",
    "torchvision==0.23.0",
    "tensorflow==2.18.0",
    "diffusers==0.35.0.dev0",
    "opencv_python==*********",
    "triton==3.4.0",
    "flash-attn==2.8.2",
    "iopath==0.1.9",
    "pyzmq",
    "pycuda",
    "nvtx",
]
thor = [
    # Thor-specific versions and packages
    "tensorflow==2.18.0",
    "diffusers==0.36.0.dev0",
    "opencv_python==*********",
    "iopath==0.1.9",
    "pyzmq",
    "nvtx",
]
deploy = [
    "tensorrt-cu12==**********",
]

[tool.setuptools.packages.find]
exclude = [
    "*.tests",
    "*.tests.*",
    "tests.*",
    "tests",
    "docs*",
    "scripts*",
    "*checkpoints*",
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
gr00t = ["py.typed"]

[tool.setuptools.dynamic]
version = {attr = "gr00t.version.VERSION"}

[tool.black]
line-length = 100
include = '\.pyi?$'
exclude = '''
(
      __pycache__
    | \.git
    | \.mypy_cache
    | \.pytest_cache
    | \.vscode
    | \.venv
    | \bdist\b
    | \bdoc\b
)
'''

[tool.isort]
profile = "black"
multi_line_output = 3

# You can override these pyright settings by adding a personal pyrightconfig.json file.
[tool.pyright]
reportPrivateImportUsage = false

[tool.ruff]
line-length = 115
target-version = "py310"

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

