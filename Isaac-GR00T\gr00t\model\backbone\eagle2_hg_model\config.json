{"_attn_implementation": "flash_attention_2", "_commit_hash": null, "architectures": ["Eagle2_5_VLForConditionalGeneration"], "auto_map": {"AutoConfig": "configuration_eagle2_5_vl.Eagle2_5_VLConfig", "AutoModel": "modeling_eagle2_5_vl.Eagle2_5_VLForConditionalGeneration"}, "downsample_ratio": 0.5, "dynamic_image_size": true, "force_image_size": 224, "image_token_index": 151669, "loss_version": "efficient_v2_cp_head", "max_dynamic_tiles": 12, "min_dynamic_tiles": 1, "mlp_checkpoint": false, "mlp_connector_layers": 1, "model_type": "eagle_2_5_vl", "pad2square": false, "select_layer": -1, "template": "qwen3-chat", "text_config": {"_attn_implementation_autoset": true, "_name_or_path": "Qwen/Qwen3-1.7B", "architectures": ["Qwen3ForCausalLM"], "attention_bias": false, "attention_dropout": 0, "bos_token_id": 151643, "eos_token_id": 151645, "head_dim": 128, "hidden_act": "silu", "hidden_size": 2048, "initializer_range": 0.02, "intermediate_size": 6144, "max_position_embeddings": 40960, "max_window_layers": 28, "model_type": "qwen3", "num_attention_heads": 16, "num_hidden_layers": 28, "num_key_value_heads": 8, "rms_norm_eps": 1e-06, "rope_scaling": null, "rope_theta": 1000000, "sliding_window": null, "tie_word_embeddings": true, "torch_dtype": "bfloat16", "use_cache": false, "use_sliding_window": false, "vocab_size": 151680}, "torch_dtype": "bfloat16", "transformers_version": null, "use_backbone_lora": 0, "use_llm_lora": 0, "use_pixel_shuffle": false, "use_thumbnail": true, "vision_config": {"_attn_implementation_autoset": true, "attention_dropout": 0, "hidden_act": "gelu_pytorch_tanh", "hidden_size": 1152, "image_size": 224, "intermediate_size": 4304, "layer_norm_eps": 1e-06, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_channels": 3, "num_hidden_layers": 27, "patch_size": 14, "torch_dtype": "bfloat16"}}