{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 3.1 New Embodiment Finetuning Tutorial (Python API)\n", "\n", "This provides a step-by-step guide on how to finetune GR00T-N1.5 with our python API, the G1 Block Stacking Dataset is used as an example.\n", "\n", "This is a more detailed version of the [3_0_new_embodiment_finetuning.md](3_0_new_embodiment_finetuning.md) tutorial, which explains in-depth the details of configuring the dataset, transforms, and finetuning.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## Step 1: Dataset\n", "\n", "Loading any dataset for finetuning can be done in 2 steps:\n", "- 1.1: Defining the modality configs and transforms for the dataset\n", "- 1.2: Loading the dataset using the `LeRobotSingleDataset` class"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step: 1.0 Download the dataset\n", "\n", "- Download the dataset from: https://huggingface.co/datasets/unitreerobotics/G1_BlockStacking_Dataset\n", "- copy over the `examples/unitree_g1_blocks__modality.json` to the dataset `<DATASET_PATH>/meta/modality.json`\n", "  - This provides additional information about the state and action modalities to make it \"GR00T-compatible\"\n", "  - `cp examples/unitree_g1_blocks__modality.json datasets/G1_BlockStacking_Dataset/meta/modality.json`\n", "\n", "\n", "**Understanding the Modality Configs**\n", "\n", "This file provides detailed metadata about state and action modalities, enabling:\n", "\n", "- **Separate Data Storage and Interpretation:**\n", "  - **State and Action:** Stored as concatenated float32 arrays. The `modality.json` file supplies the metadata necessary to interpret these arrays as distinct, fine-grained fields with additional training information.\n", "  - **Video:** Stored as separate files, with the configuration file allowing them to be renamed to a standardized format.\n", "  - **Annotations:** Keeps track of all annotation fields. If there are no annotations, do not include the `annotation` field in the configuration file.\n", "- **Fine-Grained Splitting:** Divides the state and action arrays into more semantically meaningful fields.\n", "- **Clear Mapping:** Explicit mapping of data dimensions.\n", "- **Sophisticated Data Transformations:** Supports field-specific normalization and rotation transformations during training.\n", "\n", "#### Schema\n", "\n", "```json\n", "{\n", "    \"state\": {\n", "        \"<state_name>\": {\n", "            \"start\": <int>,         // Starting index in the state array\n", "            \"end\": <int>,           // Ending index in the state array\n", "        }\n", "    },\n", "    \"action\": {\n", "        \"<action_name>\": {\n", "            \"start\": <int>,         // Starting index in the action array\n", "            \"end\": <int>,           // Ending index in the action array\n", "        }\n", "    },\n", "    \"video\": {\n", "        \"<video_name>\": {}  // Empty dictionary to maintain consistency with other modalities\n", "    },\n", "    \"annotation\": {\n", "        \"<annotation_name>\": {}  // Empty dictionary to maintain consistency with other modalities\n", "    }\n", "}\n", "```\n", "\n", "Example is shown in `getting_started/examples/unitree_g1_blocks__modality.json`. This file is located in the `meta` folder of the lerobot dataset.\n", "\n", "\n", "Generate the Stats (`meta/metadata.json`) by running the following command:\n", "```bash\n", "python scripts/load_dataset.py --data_path /datasets/G1_BlockStacking_Dataset/ --embodiment_tag new_embodiment\n", "```"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from gr00t.data.schema import EmbodimentTag"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["dataset_path = \"./demo_data/g1\"  # change this to your dataset path\n", "embodiment_tag = EmbodimentTag.NEW_EMBODIMENT"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step: 1.1 Modality configs and transforms\n", "\n", "Modality configs let you select which specific data streams to use for each input type (video, state, action, language, etc.) during finetuning, giving you precise control over which parts of your dataset are utilized."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from gr00t.data.dataset import ModalityConfig\n", "\n", "\n", "# select the modality keys you want to use for finetuning\n", "video_modality = ModalityConfig(\n", "    delta_indices=[0],\n", "    modality_keys=[\"video.cam_right_high\"],\n", ")\n", "\n", "state_modality = ModalityConfig(\n", "    delta_indices=[0],\n", "    modality_keys=[\"state.left_arm\", \"state.right_arm\", \"state.left_hand\", \"state.right_hand\"],\n", ")\n", "\n", "action_modality = ModalityConfig(\n", "    delta_indices=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],\n", "    modality_keys=[\"action.left_arm\", \"action.right_arm\", \"action.left_hand\", \"action.right_hand\"],\n", ")\n", "\n", "language_modality = ModalityConfig(\n", "    delta_indices=[0],\n", "    modality_keys=[\"annotation.human.task_description\"],\n", ")\n", "\n", "modality_configs = {\n", "    \"video\": video_modality,\n", "    \"state\": state_modality,\n", "    \"action\": action_modality,\n", "    \"language\": language_modality,\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from gr00t.data.transform.base import ComposedModalityTransform\n", "from gr00t.data.transform import VideoToTensor, VideoCrop, VideoResize, VideoColorJitter, VideoToNumpy\n", "from gr00t.data.transform.state_action import StateActionToTensor, StateActionTransform\n", "from gr00t.data.transform.concat import ConcatTransform\n", "from gr00t.model.transforms import GR00TTransform\n", "\n", "\n", "# select the transforms you want to apply to the data\n", "to_apply_transforms = ComposedModalityTransform(\n", "    transforms=[\n", "        # video transforms\n", "        VideoToTensor(apply_to=video_modality.modality_keys, backend=\"torchvision\"),\n", "        VideoCrop(apply_to=video_modality.modality_keys, scale=0.95, backend=\"torchvision\"),\n", "        VideoResize(apply_to=video_modality.modality_keys, height=224, width=224, interpolation=\"linear\", backend=\"torchvision\" ),\n", "        VideoColorJitter(apply_to=video_modality.modality_keys, brightness=0.3, contrast=0.4, saturation=0.5, hue=0.08, backend=\"torchvision\"),\n", "        VideoToNumpy(apply_to=video_modality.modality_keys),\n", "\n", "        # state transforms\n", "        StateActionToTensor(apply_to=state_modality.modality_keys),\n", "        StateActionTransform(apply_to=state_modality.modality_keys, normalization_modes={\n", "            \"state.left_arm\": \"min_max\",\n", "            \"state.right_arm\": \"min_max\",\n", "            \"state.left_hand\": \"min_max\",\n", "            \"state.right_hand\": \"min_max\",\n", "        }),\n", "\n", "        # action transforms\n", "        StateActionToTensor(apply_to=action_modality.modality_keys),\n", "        StateActionTransform(apply_to=action_modality.modality_keys, normalization_modes={\n", "            \"action.right_arm\": \"min_max\",\n", "            \"action.left_arm\": \"min_max\",\n", "            \"action.right_hand\": \"min_max\",\n", "            \"action.left_hand\": \"min_max\",\n", "        }),\n", "\n", "        # ConcatTransform\n", "        ConcatTransform(\n", "            video_concat_order=video_modality.modality_keys,\n", "            state_concat_order=state_modality.modality_keys,\n", "            action_concat_order=action_modality.modality_keys,\n", "        ),\n", "        # model-specific transform\n", "        GR00TTransform(\n", "            state_horizon=len(state_modality.delta_indices),\n", "            action_horizon=len(action_modality.delta_indices),\n", "            max_state_dim=64,\n", "            max_action_dim=32,\n", "        ),\n", "    ]\n", ")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1.2 Load the dataset\n", "\n", "First we will visualize the dataset and then load it using the `LeRobotSingleDataset` class. (without transforms)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initialized dataset g1 with EmbodimentTag.NEW_EMBODIMENT\n"]}], "source": ["from gr00t.data.dataset import LeRobotSingleDataset\n", "\n", "train_dataset = LeRobotSingleDataset(\n", "    dataset_path=dataset_path,\n", "    modality_configs=modality_configs,\n", "    embodiment_tag=embodiment_tag,\n", "    video_backend=\"torchvision_av\",\n", ")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['video.cam_right_high', 'state.left_arm', 'state.right_arm', 'state.left_hand', 'state.right_hand', 'action.left_arm', 'action.right_arm', 'action.left_hand', 'action.right_hand', 'annotation.human.task_description'])\n"]}, {"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 2000x500 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# use matplotlib to visualize the images\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "print(train_dataset[0].keys())\n", "\n", "images = []\n", "for i in range(5):\n", "    image = train_dataset[i][\"video.cam_right_high\"][0]\n", "    # image is in HWC format, convert it to CHW format\n", "    image = image.transpose(2, 0, 1)\n", "    images.append(image)   \n", "\n", "fig, axs = plt.subplots(1, 5, figsize=(20, 5))\n", "for i, image in enumerate(images):\n", "    axs[i].imshow(np.transpose(image, (1, 2, 0)))\n", "    axs[i].axis(\"off\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we will initiate a dataset with our modality configs and transforms."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initialized dataset g1 with EmbodimentTag.NEW_EMBODIMENT\n"]}], "source": ["train_dataset = LeRobotSingleDataset(\n", "    dataset_path=dataset_path,\n", "    modality_configs=modality_configs,\n", "    embodiment_tag=embodiment_tag,\n", "    video_backend=\"torchvision_av\",\n", "    transforms=to_apply_transforms,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Extra Notes**:\n", " - We use a cached dataloader to accelerate training speed. The cached dataloader loads all data into memory, which significantly improves training performance. However, if your dataset is large or you're experiencing out-of-memory (OOM) errors, you can switch to the standard lerobot dataloader (`gr00t.data.dataset.LeRobotSingleDataset`). It uses the same API as the cached dataloader, so you can switch back and forth without any changes to your code.\n", " - we use torchvision_av as the video backend, the video encoding is in av instead of standard h264\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Load the model\n", "\n", "The training process is done in 3 steps:\n", "- 2.1: Load the base model from HuggingFace or a local path\n", "- 2.2: Prepare training args\n", "- 2.3: Run the training loop"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2.1 Load the base model\n", "\n", "We'll use the `from_pretrained_for_tuning` method to load the model. This method allows us to specify which parts of the model to tune."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gr00t.model.gr00t_n1 import GR00T_N1_5\n", "\n", "BASE_MODEL_PATH = \"nvidia/GR00T-N1.5-3B\"\n", "TUNE_LLM = False            # Whether to tune the LLM\n", "TUNE_VISUAL = False          # Whether to tune the visual encoder\n", "TUNE_PROJECTOR = True       # Whether to tune the projector\n", "TUNE_DIFFUSION_MODEL = True # Whether to tune the diffusion model\n", "\n", "model = GR00T_N1_5.from_pretrained(\n", "    pretrained_model_name_or_path=BASE_MODEL_PATH,\n", "    tune_llm=TUNE_LLM,  # backbone's LLM\n", "    tune_visual=TUNE_VISUAL,  # backbone's vision tower\n", "    tune_projector=TUNE_PROJECTOR,  # action head's projector\n", "    tune_diffusion_model=TUNE_DIFFUSION_MODEL,  # action head's DiT\n", ")\n", "\n", "# Set the model's compute_dtype to bfloat16\n", "model.compute_dtype = \"bfloat16\"\n", "model.config.compute_dtype = \"bfloat16\"\n", "model.to(device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2.2 Prepare training args\n", "\n", "We use huggingface `TrainingArguments` to configure the training process. Here are the main parameters:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n"]}], "source": ["from transformers import TrainingArguments\n", "\n", "output_dir = \"output/model/path\"    # CHAN<PERSON> THIS ACCORDING TO YOUR LOCAL PATH\n", "per_device_train_batch_size = 8     # CHANGE THIS ACCORDING TO YOUR GPU MEMORY\n", "max_steps = 20                      # <PERSON><PERSON><PERSON> THIS ACCORDING TO YOUR NEEDS\n", "report_to = \"wandb\"\n", "dataloader_num_workers = 8\n", "\n", "training_args = TrainingArguments(\n", "    output_dir=output_dir,\n", "    run_name=None,\n", "    remove_unused_columns=False,\n", "    deepspeed=\"\",\n", "    gradient_checkpointing=False,\n", "    bf16=True,\n", "    tf32=True,\n", "    per_device_train_batch_size=per_device_train_batch_size,\n", "    gradient_accumulation_steps=1,\n", "    dataloader_num_workers=dataloader_num_workers,\n", "    dataloader_pin_memory=False,\n", "    dataloader_persistent_workers=True,\n", "    optim=\"adamw_torch\",\n", "    adam_beta1=0.95,\n", "    adam_beta2=0.999,\n", "    adam_epsilon=1e-8,\n", "    learning_rate=1e-4,\n", "    weight_decay=1e-5,\n", "    warmup_ratio=0.05,\n", "    lr_scheduler_type=\"cosine\",\n", "    logging_steps=10.0,\n", "    num_train_epochs=300,\n", "    max_steps=max_steps,\n", "    save_strategy=\"steps\",\n", "    save_steps=500,\n", "    save_total_limit=8,\n", "    report_to=report_to,\n", "    seed=42,\n", "    do_eval=False,\n", "    ddp_find_unused_parameters=False,\n", "    ddp_bucket_cap_mb=100,\n", "    torch_compile_mode=None,\n", ")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2.3 Initialize the training runner and run the training loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gr00t.experiment.runner import TrainRunner\n", "\n", "experiment = TrainRunner(\n", "    train_dataset=train_dataset,\n", "    model=model,\n", "    training_args=training_args,\n", ")\n", "\n", "experiment.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the 1k offline validation results vs 10k offline validation results:\n", "\n", "**Finetuning Results on Unitree G1 Block Stacking Dataset:**\n", "\n", "| 1k Steps | 10k Steps |\n", "| --- | --- |\n", "| ![1k](../media/g1_ft_1k.png) | ![10k](../media/g1_ft_10k.png) |\n", "| MSE: 0.0181 | MSE: 0.0022 |"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "gr00t", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}